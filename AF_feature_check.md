# AF Feature Check - Prompt Generator System Analysis

*Comparison of current implementation against desired feature set*

## Current System Overview
The existing prompt_generator implements a **multi-agent orchestration system** with 5 specialized agents (<PERSON>, <PERSON><PERSON>, Editor, TokenOptimizer, OutputGuardian) that iteratively improve prompts for private equity research tasks.

---

## Feature Analysis

### ❌ **Consensus Engine**
**Status:** Not implemented  
**Current:** Single-path orchestration with deterministic agent selection  
**Gap:** No consensus mechanism between multiple prompt versions or agents

### ✅ **Quality Gate** 
**Status:** Implemented via Critic Agent  
**Location:** `prompt_generator/roles/critic.py:10-23`  
**Details:** 
- Scores prompts 0-10 with decimal precision
- Uses target_score threshold (default 8.5) from `config.yaml:9`
- Blocks progression until quality criteria met
- Provides actionable feedback for improvements

### ❌ **A|B Testing Framework**
**Status:** Not implemented  
**Gap:** No systematic comparison of prompt variants or performance testing

### ❌ **Prompt Registration System**
**Status:** No central registry  
**Current:** Prompts exist only during orchestration session  
**Gap:** No persistence, versioning, or cataloging of generated prompts

### ❌ **Simplified Prompt Versions**
**Status:** Only full versions exist  
**Gap:** No abstracted or compressed representations for quick reference

### ✅ **Output Guardian**
**Status:** Implemented  
**Location:** `prompt_generator/roles/output_guardian.py:16-17`  
**Function:** Validates format compliance (checks for `JSON_OUTPUT:` or `MARKDOWN_OUTPUT:` markers)  
**Limitation:** Only format validation, not content or safety guardrails

### ✅ **Agent Framework**
**Status:** Custom implementation  
**Details:**
- Base class: `prompt_generator/roles/base.py`
- Auto-discovery of agent roles
- Shared state management
- Deterministic orchestration policy in `orchestrator.py`

### ✅ **Grader Prompts & Scoring**
**Status:** Implemented via Critic Agent  
**Scoring Method:**
- **Rubric-based:** 5 criteria (Clarity, Specificity, Context, Format guidance, Constraints)
- **Prescriptive metaprompt:** Highly structured system prompt with mandatory score format
- **Output requirement:** Must end with "Score: X.Y/10"
- **Actionable feedback:** Provides specific improvement suggestions

### ✅ **Token Optimizer**
**Status:** Implemented  
**Location:** `prompt_generator/roles/token_optimizer.py:24-42`  
**Purpose:** 
- Reduce instruction creep and token bloat
- Compress prompts while preserving semantic meaning
- Triggered only after quality gate is passed

**Protection Mechanisms:**
- Explicit preservation rules for critical elements
- Cannot modify role titles, wrapper tags, placeholders
- Must preserve constraints (confidentiality, GAAP accuracy, refusal rules)
- Maintains section headers and output descriptions
- Configuration limits: min/max savings percentages in `config.yaml:12-16`

---

## Gap Analysis: Desired vs Current Architecture

### ❌ **Missing Components:**

#### 1. **Structured Document Generation Pipeline**
```
Current: task → Writer → draft
Desired: INPUT → MasterPrompt + Requirements Doc + TestCase Doc
```

#### 2. **Requirements Decomposition**
```
Desired: Requirements → list → individual Grader scoring
Current: Single Critic evaluation
```

#### 3. **TestCase Generation Framework**
```
Desired: TestCaseDocGenerator(instructions, requirements) → test_case_seeds → TestCaseGenerator
Current: Web interface has basic test data generation (app.py:195-356) but not integrated
```

#### 4. **Pydantic Models**
```
Current: Dict-based state management
Desired: Structured models for MasterPrompt, Requirements, TestCases, Grading
```

#### 5. **Interactive TestCase Generator**
```
Desired: [more diverse] + [less diverse] variants, 30 parallel generation
Current: Sequential single-path generation
```

#### 6. **Template Engine (Jinja)**
```
Desired: Jinja templates for generation logic
Current: String formatting and f-strings
```

---

## Current Test Data Generation (Partial Implementation)

**Location:** `app.py:195-356`  
**Capabilities:**
- Test data type planning
- Parallel test case generation (via Flask background tasks)
- Realistic input generation with placeholders
- JSON output format

**Limitations:**
- Web interface only, not CLI accessible
- Not integrated with main orchestration flow
- No diversity controls or iteration mechanisms

---

## Recommendations

### **High Priority:**
1. **Add Pydantic Models** - Structure data flow with proper typing
2. **Implement Requirements Doc Generator** - Break down tasks into measurable requirements
3. **Create TestCase Pipeline** - Integrate existing test generation with orchestration
4. **Add Prompt Registry** - Persist and version generated prompts

### **Medium Priority:**
5. **A|B Testing Framework** - Compare prompt variants systematically  
6. **Consensus Engine** - Enable multi-path prompt generation with voting
7. **Enhanced Output Guardian** - Content safety and compliance checking

### **Low Priority:**
8. **Jinja Template Engine** - Replace string formatting with proper templating
9. **Interactive CLI** - Tree-based context management for complex tasks
10. **Simplified Prompt Views** - Abstract representations for quick reference

---

## Current Strengths
- ✅ **Quality-focused:** Iterative improvement until target score met
- ✅ **Domain-specific:** PE research specialization with compliance guardrails  
- ✅ **Token-efficient:** Compression without meaning loss
- ✅ **Deterministic:** Predictable orchestration flow
- ✅ **Extensible:** Plugin-style agent architecture

## Architecture Gaps
- ❌ **Single-threaded:** No parallel prompt generation or comparison
- ❌ **Session-bound:** No persistence or prompt management
- ❌ **Monolithic evaluation:** Single critic vs. decomposed requirements scoring
- ❌ **Limited test integration:** Test generation exists but not orchestrated
- ❌ **String-based:** No structured data models or templating

---

## Architecture Comparison

### 🎯 **AF's Desired Design System**

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           AF'S PROPOSED ARCHITECTURE                           │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  INPUT: Initial_instruction_from_user                                          │
│    │                                                                            │
│    ▼                                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    GENERATION LAYER                                     │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┐ │   │
│  │  │   MasterPrompt  │  │ Requirements    │  │    TestCase Doc         │ │   │
│  │  │   Generator     │  │ Doc Generator   │  │    Generator            │ │   │
│  │  │                 │  │                 │  │                         │ │   │
│  │  │ def Generator   │  │ (natural lang)  │  │ TestCaseDocGenerator(   │ │   │
│  │  │ (INPUT,         │  │                 │  │   instructions,         │ │   │
│  │  │  logic)         │  │                 │  │   requirements)         │ │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│              │                        │                        │               │
│              ▼                        ▼                        ▼               │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                     DECOMPOSITION LAYER                                │   │
│  │                                                                         │   │
│  │  Requirements → list[requirement]    TestCaseDoc → test_case_seeds:    │   │
│  │       │                                     │                          │   │
│  │       ▼                                     ▼                          │   │
│  │  ┌──────────────┐                    ┌─────────────────────────────┐   │   │
│  │  │  Individual  │ ×N                 │    TestCaseGenerator        │   │   │
│  │  │   Grader     │                    │                             │   │   │
│  │  │              │                    │ test_cases = [              │   │   │
│  │  │ LLM_scoring_ │                    │   TestCaseGenerator(seed)   │   │   │
│  │  │ judge() →    │                    │   for seed in seeds]        │   │   │
│  │  │ {score:≤100, │                    │                             │   │   │
│  │  │  justif:str} │                    └─────────────────────────────┘   │   │
│  │  └──────────────┘                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│              │                                     │                           │
│              ▼                                     ▼                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    INTERACTIVE LAYER                                   │   │
│  │                                                                         │   │
│  │  ┌───────────────────────────────────────────────────────────────────┐ │   │
│  │  │           Interactive TestCase Generator                          │ │   │
│  │  │                                                                   │ │   │
│  │  │  Generator prompt needs honing → Generate:                       │ │   │
│  │  │  • [more diverse] + [less diverse] (pre-loaded)                  │ │   │
│  │  │  • 30 test cases in parallel                                     │ │   │
│  │  │  • After TestCaseDoc iteration                                   │ │   │
│  │  └───────────────────────────────────────────────────────────────────┘ │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                    │                                           │
│                                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                      TEMPLATING ENGINE                                 │   │
│  │                                                                         │   │
│  │  JINJA Templates with overlap between generators                       │   │
│  │  OR Class with non-interactive CLI for CC interaction                  │   │
│  │  OR Interactive with tree-constraining context + feedback             │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                    │                                           │
│                                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                      PYDANTIC MODELS                                   │   │
│  │                                                                         │   │
│  │  • MasterPrompt                                                        │   │
│  │  • RequirementsDoc                                                     │   │
│  │  • TestCaseDoc                                                         │   │
│  │  • GraderResult                                                        │   │
│  │  • TestCase                                                            │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 🏗️ **Current System Architecture**

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│                          CURRENT IMPLEMENTATION                                │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  task: str (user input)                                                        │
│    │                                                                            │
│    ▼                                                                            │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                   ORCHESTRATION LAYER                                  │   │
│  │                                                                         │   │
│  │  Writer → OutputGuardian → Critic → Editor ⟷ Critic → TokenOptimizer   │   │
│  │    │           │             │         │         │           │         │   │
│  │    ▼           ▼             ▼         ▼         ▼           ▼         │   │
│  │ [draft]   [format_ok] [critic_score] [improved] [re-score] [optimized] │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                    │                                           │
│                                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                       STATE MANAGEMENT                                 │   │
│  │                                                                         │   │
│  │  Dict[str, Any] shared between agents:                                 │   │
│  │  • task, draft, critic_score, critic_feedback                          │   │
│  │  • output_guardian_pass, token_saving_pct                              │   │
│  │  • _last_role, log                                                     │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                    │                                           │
│                                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                    TEST DATA (WEB ONLY)                                │   │
│  │                                                                         │   │
│  │  app.py: Basic test data generation                                    │   │
│  │  • Test data type planning                                             │   │
│  │  • Individual test case generation                                     │   │
│  │  • NOT integrated with orchestration                                   │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
│                                    │                                           │
│                                    ▼                                           │
│  ┌─────────────────────────────────────────────────────────────────────────┐   │
│  │                       OUTPUT                                            │   │
│  │                                                                         │   │
│  │  Final prompt (string) + orchestration history                         │   │
│  │  No persistence, no structured models                                  │   │
│  └─────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 🔄 **Feature Comparison Matrix**

```ascii
┌─────────────────────────────────────────────────────────────────────────────────┐
│                           FEATURE COMPARISON                                   │
├─────────────────────────────────────────────────────────────────────────────────┤
│                                                                                 │
│  Feature                          │ AF Design │ Current │ Status               │
│  ─────────────────────────────────────────────────────────────────────────────  │
│  MasterPrompt Generation          │    ✅     │   ❌    │ Need structured gen   │
│  Requirements Doc Generation      │    ✅     │   ❌    │ Missing decomposition │
│  TestCase Doc Generation          │    ✅     │   🤷    │ Partial (web only)   │
│  Individual Requirement Grading   │    ✅     │   ❌    │ Only holistic critic │
│  Parallel Test Case Generation    │    ✅     │   🤷    │ Basic parallel in web │
│  Interactive Test Diversity       │    ✅     │   ❌    │ Missing controls      │
│  Pydantic Data Models            │    ✅     │   ❌    │ Dict-based state      │
│  JINJA Template Engine           │    ✅     │   ❌    │ String formatting     │
│  Quality Gate System             │    ✅     │   ✅    │ Implemented (Critic)  │
│  Multi-Agent Orchestration       │    🤷     │   ✅    │ Current has this      │
│  Token Optimization              │    🤷     │   ✅    │ Current has this      │
│  Format Validation               │    🤷     │   ✅    │ OutputGuardian        │
│  Consensus Engine                │    ❌     │   ❌    │ Neither has this      │
│  A|B Testing Framework           │    ❌     │   ❌    │ Neither has this      │
│  Prompt Registry/Persistence     │    🤷     │   ❌    │ Implied in AF design  │
│                                                                                 │
└─────────────────────────────────────────────────────────────────────────────────┘
```

### 🎯 **Key Architectural Differences**

#### **AF Design Philosophy:**
```ascii
INPUT → [Structured Generation] → [Decomposed Evaluation] → [Interactive Refinement]
  │              │                        │                       │
  │              ▼                        ▼                       ▼
  │      3 Parallel Docs         Individual Graders    Diversity Controls
  │      (Master/Req/Test)       (Per Requirement)     (30 Parallel Tests)
  │
  └─── Pydantic Models + JINJA Templates + Persistent Registry
```

#### **Current System Philosophy:**
```ascii
task → [Sequential Agents] → [Holistic Evaluation] → [Iterative Improvement]
  │           │                      │                      │
  │           ▼                      ▼                      ▼
  │    Writer→Guardian→Critic    Single Score (0-10)   Editor Revisions
  │
  └─── Dict State + String Templates + Session-Only
```

### 📊 **Transformation Requirements**

#### **🔴 Major Gaps (High Priority):**
- ❌ **Requirements Decomposition:** Need to break tasks into measurable components
- ❌ **Structured Data Models:** Replace Dict with Pydantic models
- ❌ **Individual Grading:** Score each requirement separately vs. holistic
- ❌ **Document Generation Pipeline:** MasterPrompt + Requirements + TestCase docs

#### **🟡 Medium Gaps:**
- 🤷 **Test Integration:** Connect existing test generation to orchestration
- 🤷 **Parallel Processing:** Scale from sequential to parallel generation
- 🤷 **Template Engine:** Replace string formatting with JINJA

#### **🟢 Current Strengths to Preserve:**
- ✅ **Quality Gate:** Critic agent with scoring threshold
- ✅ **Orchestration Logic:** Multi-agent coordination works well
- ✅ **Token Optimization:** Compression without meaning loss
- ✅ **Format Validation:** OutputGuardian for compliance