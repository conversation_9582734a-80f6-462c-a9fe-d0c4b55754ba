# Default configuration for prompt_generator
roles:
  - Writer
  - OutputGuardian
  - Critic
  - Editor
  - TokenOptimizer

target_score: 8.5
max_turns: 10

# TokenOptimizer configuration
token_optimizer:
  min_savings_pct: 10  # Minimum percentage of token savings required to apply optimization
  min_tokens_saved: 20  # Minimum absolute number of tokens that must be saved
  max_savings_pct: 10   # Maximum percentage of token savings allowed (prevents over-optimization)

# Model configuration
model_settings:
  reasoning_effort: "high"  # Options: "low", "medium", "high"
  max_completion_tokens: 12000  # Maximum tokens for o3 models

# Optional: hard-coded OpenAI key (discouraged for production). The library
# will use this if OPENAI_API_KEY env var is not set.
openai_api_key: "*********************************************************************************************************************************************************"

# Fireworks API key (uncomment and add your key when needed)
# fireworks_api_key: "fw-..."

# Optional: Base URLs (uncomment if you need to use a different endpoint)
# openai_base_url: "https://api.openai.com/v1"
# fireworks_base_url: "https://api.fireworks.ai/inference/v1"

