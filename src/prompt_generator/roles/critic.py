"""Critic role - evaluates quality of the prompt and suggests improvements."""

import re
from typing import Any, Dict, Optional

from .base import BaseRole, RoleOutput


class Critic(BaseRole):
    system_prompt = """You are a Prompt Engineering Critic with expertise in creating effective prompts for LLMs.
    Your task is to evaluate the quality of a prompt based on the following criteria:
    
    1. Clarity: Is the prompt clear about what is being asked?
    2. Specificity: Does it include enough details to guide the response?
    3. Context: Does it provide necessary background information?
    4. Format guidance: Does it specify how the answer should be structured?
    5. Constraints: Does it include relevant constraints or guardrails?
    
    You MUST assign a score from 0-10 with one decimal place (e.g., 7.5).
    You MUST include "Score: X.Y/10" at the end of your feedback.
    
    Provide specific, actionable feedback that would help improve the prompt.
    """

    def eval(self, state: Dict[str, Any]) -> RoleOutput:
        draft = state.get("draft", "")
        if not draft:
            return RoleOutput({"log": "Critic skipped – no draft to evaluate."})

        user_prompt = (
            f"Task: {state.get('task', 'No task provided')}\n\n"
            f"Draft prompt to evaluate:\n\n{draft}\n\n"
            f"Please evaluate this prompt on a scale of 0-10 (with one decimal place), "
            f"and provide specific feedback for improvement. End with 'Score: X.Y/10'."
        )
        
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_prompt},
        ]
        
        feedback = self._call_llm(messages)
        
        # Extract score using regex - look for "Score: X.Y/10" pattern
        score_match = re.search(r"Score:\s*(\d+\.\d+)\/10", feedback)
        score: Optional[float] = None
        
        if score_match:
            try:
                score = float(score_match.group(1))
            except (ValueError, IndexError):
                # Handle invalid score format
                print("Warning: Could not parse score from critic feedback")
                score = 7.0  # Default score for demo purposes
        else:
            # If score not found, use a fallback
            print("Warning: No score found in critic feedback")
            score = 7.0  # Default score for demo purposes
            feedback += "\nScore: 7.0/10"
        
        # Validate score range
        if score is not None:
            if score < 0 or score > 10:
                print(f"Warning: Score {score} out of range 0-10, clamping")
                score = max(0, min(10, score))
        
        return RoleOutput({
            "critic_feedback": feedback,
            "critic_score": score,
            "log": f"Critic evaluated prompt. Score: {score}/10"
        }) 