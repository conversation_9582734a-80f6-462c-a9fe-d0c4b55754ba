"""Command-line interface for prompt generator."""

from __future__ import annotations

import json
import sys
import datetime
import difflib
from pathlib import Path
from typing import Optional, Dict, Any, List, TextIO

import typer
from rich.console import Console
from rich.syntax import Syntax
from rich.panel import Panel
from rich.columns import Columns
from rich.text import Text
from rich.markdown import Markdown

from .core import run_orchestrator
from .config import load_config

app = typer.Typer(add_help_option=True, pretty_exceptions_enable=False)
console = Console()

# Load defaults from config.yaml
config = load_config()
DEFAULT_TARGET_SCORE = config.get("target_score", 8.0)
DEFAULT_MAX_TURNS = config.get("max_turns", 10)


def format_diff(old_text: str, new_text: str) -> Text:
    """Generate rich-formatted text showing differences between old and new text."""
    diff_lines = []
    for line in difflib.unified_diff(
        old_text.splitlines(),
        new_text.splitlines(),
        lineterm='',
        n=2  # Context lines
    ):
        if line.startswith('+'):
            diff_lines.append(f"[green]{line}[/green]")
        elif line.startswith('-'):
            diff_lines.append(f"[red]{line}[/red]")
        elif line.startswith('@@'):
            diff_lines.append(f"[cyan]{line}[/cyan]")
        else:
            diff_lines.append(line)
    
    return Text.from_markup("\n".join(diff_lines))


def display_side_by_side(version1: str, version2: str, title1: str = "Before", title2: str = "After") -> None:
    """Display two versions of text side by side in the terminal."""
    panel1 = Panel(Syntax(version1, "markdown", theme="monokai", line_numbers=True), title=title1)
    panel2 = Panel(Syntax(version2, "markdown", theme="monokai", line_numbers=True), title=title2)
    
    console.print(Columns([panel1, panel2]))


@app.command()
def orchestrate(
    task: str = typer.Option(..., "--task", "-t", help="Task description for which to generate/refine a prompt."),
    target_score: float = typer.Option(DEFAULT_TARGET_SCORE, help="Target critic score required to stop (0-10)."),
    max_turns: int = typer.Option(DEFAULT_MAX_TURNS, help="Maximum turns before giving up."),
    save: Optional[Path] = typer.Option(None, help="Optional path to save final prompt as markdown."),
    verbose: bool = typer.Option(False, "--verbose", "-v", help="Print detailed information about each step."),
    log_file: Optional[Path] = typer.Option(None, "--log", "-l", help="Save detailed logs to a text file."),
    side_by_side: bool = typer.Option(False, "--side-by-side", "-s", help="Show side-by-side view of prompt changes."),
    html_report: Optional[Path] = typer.Option(None, "--html", "-h", help="Generate HTML report of prompt evolution."),
):
    """Run the orchestrator interactively and display the evolution of the prompt."""
    # Use timestamp in filename if no log file specified
    if log_file is None and verbose:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = Path(f"prompt_gen_{timestamp}.log")
    
    state, history = run_orchestrator_verbose(task, target_score=target_score, max_turns=max_turns, 
                                             verbose=verbose, log_file=log_file, 
                                             side_by_side=side_by_side)

    console.rule("[bold green]Orchestration complete")

    final_prompt = state.get("draft", "<no prompt generated>")
    syntax = Syntax(final_prompt, "markdown", theme="monokai", line_numbers=False)
    console.print(syntax)

    console.print("\n[bold]Termination reason:[/bold]", state.get("termination_reason"))

    if save:
        save.write_text(final_prompt)
        console.print(f"\n[green]Saved final prompt to {save.resolve()}[/green]")
    
    if log_file:
        console.print(f"\n[green]Detailed interaction log saved to {log_file.resolve()}[/green]")

    if html_report:
        html_path = generate_html_report(state, history, html_report)
        console.print(f"\n[green]HTML evolution report saved to {html_path.resolve()}[/green]")

    if console.is_terminal:
        # Offer to print JSON history
        if typer.confirm("Show orchestration history?", default=False):
            console.print_json(json.dumps(history, indent=2))


def run_orchestrator_verbose(task_description: str, *, target_score: float = DEFAULT_TARGET_SCORE, 
                            max_turns: int = DEFAULT_MAX_TURNS, verbose: bool = False, 
                            log_file: Optional[Path] = None, side_by_side: bool = False) -> tuple:
    """Run the orchestrator with optional verbose output and detailed logging."""
    # Need to import here to avoid circular import issues
    from .orchestrator import Orchestrator
    
    orchestrator = Orchestrator(target_score=target_score, max_turns=max_turns)
    
    # Original state and empty history
    state = {
        "task": task_description,
        "target_score": target_score,
    }
    history = []
    
    if verbose:
        console.print("[bold blue]Starting orchestration process...[/bold blue]")
    
    # Set up log file if specified
    log_handle = None
    if log_file:
        try:
            log_handle = open(log_file, "w", encoding="utf-8")
            _write_log(log_handle, f"=== PROMPT GENERATOR LOG ===\n")
            _write_log(log_handle, f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            _write_log(log_handle, f"Task: {task_description}\n")
            _write_log(log_handle, f"Target Score: {target_score}\n")
            _write_log(log_handle, f"Max Turns: {max_turns}\n")
            _write_log(log_handle, f"{'='*50}\n\n")
        except Exception as e:
            console.print(f"[bold red]Error opening log file: {e}[/bold red]")
            log_handle = None
    
    # Track the previous draft for side-by-side comparison
    previous_draft = ""
    
    for turn in range(1, max_turns + 1):
        role_name, reason = orchestrator._policy(state)
        if role_name is None:
            state["termination_reason"] = reason
            if verbose:
                console.print(f"[bold red]Terminating: {reason}[/bold red]")
            if log_handle:
                _write_log(log_handle, f"\n--- TERMINATION: {reason} ---\n")
            break
            
        role = orchestrator.roles.get(role_name)
        if role is None:
            raise KeyError(f"Role '{role_name}' not found. Ensure it exists in roles package.")
        
        if verbose:
            console.print(f"[bold cyan]Turn {turn}:[/bold cyan] Running role [bold yellow]{role_name}[/bold yellow] - {reason}")
        
        if log_handle:
            _write_log(log_handle, f"\n{'='*80}\n")
            _write_log(log_handle, f"TURN {turn}: Role {role_name} - {reason}\n")
            _write_log(log_handle, f"{'='*80}\n\n")
            _write_log(log_handle, f"--- INPUT STATE ---\n")
            # Print relevant parts of the state that will be used by this role
            _write_log(log_handle, f"Task: {state.get('task', '')}\n")
            if 'draft' in state:
                _write_log(log_handle, f"Current Draft:\n{state['draft']}\n\n")
            if 'critic_score' in state:
                _write_log(log_handle, f"Critic Score: {state['critic_score']}\n")
                if 'critic_feedback' in state:
                    _write_log(log_handle, f"Critic Feedback: {state['critic_feedback']}\n\n")
            _write_log(log_handle, f"--- END INPUT STATE ---\n\n")
        
        # Capture the draft before running the role
        previous_draft = state.get("draft", "")
        
        # Capture the state before running the role to compare changes
        state_before = state.copy()
        
        output = role.eval(state)
        state.update(output.to_state_update())
        # Track the last role executed to avoid potential infinite loops
        state["_last_role"] = role_name
        
        # Check for draft changes for side-by-side comparison
        current_draft = state.get("draft", "")
        if side_by_side and 'draft' in state and previous_draft and current_draft != previous_draft:
            console.rule(f"[bold green]Changes after {role_name}[/bold green]")
            
            # Show side-by-side comparison
            display_side_by_side(previous_draft, current_draft, "Before", "After")
            
            # Show feedback if available
            if "critic_feedback" in state and role_name == "Critic":
                feedback_panel = Panel(
                    Markdown(state["critic_feedback"]),
                    title=f"Critic Feedback (Score: {state.get('critic_score', 'N/A')})",
                    border_style="yellow"
                )
                console.print(feedback_panel)
            
            # Or show other role outputs
            elif "log" in state:
                log_panel = Panel(Text(state["log"]), title=f"{role_name} Output", border_style="blue")
                console.print(log_panel)
        
        if log_handle:
            _write_log(log_handle, f"--- OUTPUT FROM {role_name} ---\n")
            # Find and log changes to the state
            for key, value in state.items():
                if key not in state_before or state_before[key] != value:
                    if key == 'draft':
                        _write_log(log_handle, f"Updated Draft:\n{value}\n\n")
                    elif key != '_last_role' and key != 'log':
                        _write_log(log_handle, f"Updated {key}: {value}\n")
            if 'log' in state:
                _write_log(log_handle, f"Log: {state['log']}\n")
            _write_log(log_handle, f"--- END OUTPUT FROM {role_name} ---\n\n")
        
        if verbose and "log" in state:
            console.print(f"  -> {state['log']}")
            
        history.append({
            "turn": turn,
            "role": role_name,
            "reason": reason,
            "output_log": state.get("log", ""),
            "draft": state.get("draft", ""),  # Store the draft at this point for history
            "critic_score": state.get("critic_score") if role_name == "Critic" else None,
            "critic_feedback": state.get("critic_feedback") if role_name == "Critic" else None,
        })
        
        # Clear single-use log to avoid clutter
        state.pop("log", None)
    
    # Close log file if open
    if log_handle:
        _write_log(log_handle, f"\n{'='*50}\n")
        _write_log(log_handle, f"FINAL GENERATED PROMPT:\n\n{state.get('draft', '<no prompt generated>')}\n")
        _write_log(log_handle, f"\n{'='*50}\n")
        log_handle.close()
        
    return state, history


def _write_log(file_handle: TextIO, text: str) -> None:
    """Helper function to write to log file with error handling."""
    try:
        file_handle.write(text)
        file_handle.flush()
    except Exception:
        pass  # Silently continue if logging fails


def generate_html_report(state: Dict[str, Any], history: List[Dict[str, Any]], 
                         output_path: Optional[Path] = None) -> Path:
    """Generate an HTML report visualizing the evolution of the prompt through turns."""
    if output_path is None:
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        output_path = Path(f"prompt_evolution_{timestamp}.html")
    
    # Generate HTML
    html = [
        "<!DOCTYPE html>",
        "<html>",
        "<head>",
        "    <title>Prompt Evolution Report</title>",
        "    <style>",
        "        body { font-family: Arial, sans-serif; margin: 20px; }",
        "        .turn { margin-bottom: 30px; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }",
        "        .role { font-weight: bold; color: #2c3e50; }",
        "        .feedback { background-color: #f8f9fa; padding: 10px; border-left: 4px solid #ffc107; margin: 10px 0; }",
        "        .score { font-weight: bold; color: #e74c3c; }",
        "        .diff { overflow: auto; max-height: 500px; }",
        "        table.diff { font-family: monospace; border-collapse: collapse; }",
        "        .diff_header { background-color: #e0e0e0; }",
        "        td.diff_header { text-align: right; }",
        "        .diff_add { background-color: #aaffaa; }",
        "        .diff_chg { background-color: #ffff77; }",
        "        .diff_sub { background-color: #ffaaaa; }",
        "        .summary { margin-top: 20px; padding: 15px; background-color: #f0f7fb; border-left: 5px solid #3498db; }",
        "        .side-by-side { display: flex; }",
        "        .version { flex: 1; margin: 10px; padding: 10px; border: 1px solid #ddd; }",
        "        pre { white-space: pre-wrap; word-wrap: break-word; }",
        "    </style>",
        "</head>",
        "<body>",
        f"    <h1>Prompt Evolution Report</h1>",
        f"    <p>Task: {state.get('task', 'No task specified')}</p>",
        f"    <p>Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>",
        "    <div class=\"summary\">",
        f"        <h2>Summary</h2>",
        f"        <p>Total turns: {len(history)}</p>",
        f"        <p>Target score: {state.get('target_score', 'Not specified')}</p>",
        f"        <p>Final score: {state.get('critic_score', 'Not available')}</p>",
        f"        <p>Termination reason: {state.get('termination_reason', 'Not specified')}</p>",
        "    </div>"
    ]
    
    # Add each turn with drafts where available
    prev_draft = ""
    for turn_data in history:
        turn_num = turn_data["turn"]
        role = turn_data["role"]
        current_draft = turn_data.get("draft", "")
        
        html.append(f"    <div class=\"turn\">")
        html.append(f"        <h2>Turn {turn_num}: {role}</h2>")
        
        # Add feedback and score if available
        if turn_data.get("critic_feedback") or turn_data.get("critic_score") is not None:
            html.append(f"        <div class=\"feedback\">")
            if turn_data.get("critic_feedback"):
                html.append(f"            <p>{turn_data['critic_feedback']}</p>")
            if turn_data.get("critic_score") is not None:
                html.append(f"            <p class=\"score\">Score: {turn_data['critic_score']}</p>")
            html.append(f"        </div>")
        elif turn_data.get("output_log"):
            html.append(f"        <div class=\"feedback\">")
            html.append(f"            <p>{turn_data['output_log']}</p>")
            html.append(f"        </div>")
        
        # Show changes if draft is available and has changed
        if current_draft and current_draft != prev_draft:
            if prev_draft:
                # Generate side-by-side comparison
                html.append(f"        <h3>Prompt Changes</h3>")
                html.append(f"        <div class=\"side-by-side\">")
                html.append(f"            <div class=\"version\">")
                html.append(f"                <h4>Previous Version</h4>")
                html.append(f"                <pre>{prev_draft}</pre>")
                html.append(f"            </div>")
                html.append(f"            <div class=\"version\">")
                html.append(f"                <h4>New Version</h4>")
                html.append(f"                <pre>{current_draft}</pre>")
                html.append(f"            </div>")
                html.append(f"        </div>")
                
                # Generate HTML diff
                differ = difflib.HtmlDiff(wrapcolumn=80)
                diff_html = differ.make_file(prev_draft.splitlines(), current_draft.splitlines(),
                                          "Previous Version", "New Version", context=True)
                
                html.append(f"        <h3>Detailed Changes</h3>")
                html.append(f"        <div class=\"diff\">{diff_html}</div>")
            else:
                # First version - just show it without diff
                html.append(f"        <h3>Initial Draft</h3>")
                html.append(f"        <pre>{current_draft}</pre>")
            
            prev_draft = current_draft
        
        html.append(f"    </div>")
    
    # Add final prompt
    html.append("    <h2>Final Prompt</h2>")
    html.append(f"    <pre>{state.get('draft', 'No final draft available')}</pre>")
    
    # Close HTML
    html.append("</body>")
    html.append("</html>")
    
    # Write HTML to file
    output_path.write_text("\n".join(html))
    return output_path


def main():
    """Entry point function for the CLI."""
    app()


if __name__ == "__main__":  # pragma: no cover
    main() 